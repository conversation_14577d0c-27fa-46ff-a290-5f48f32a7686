<!-- Results Summary -->
<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
    <div class="text-sm text-gray-700">
        {% if page_obj.paginator.count %}
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} ordinances
        {% else %}
            No ordinances found
        {% endif %}
    </div>

    {% if search_query or selected_category or selected_status %}
        <div class="mt-2 sm:mt-0">
            <a href="{% url 'ordinances:admin_ordinance_list' %}"
               class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                Clear all filters
            </a>
        </div>
    {% endif %}
</div>

<!-- Ordinances Table -->
{% if page_obj.object_list %}
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ordinance
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        PDF
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Year
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                    </th>
                    <th class="relative px-6 py-3">
                        <span class="sr-only">Actions</span>
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for ordinance in page_obj.object_list %}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div>
                                <div class="text-sm font-medium text-gray-900">
                                    {{ ordinance.ordinance_number }}
                                </div>
                                <div class="text-sm text-gray-500 max-w-xs">
                                    {{ ordinance.title|truncatechars:60 }}
                                </div>
                                {% if ordinance.sponsors.exists %}
                                    <div class="text-xs text-gray-400 mt-1">
                                        {{ ordinance.sponsors.count }} sponsor{{ ordinance.sponsors.count|pluralize }}
                                    </div>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if ordinance.category %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                                    {{ ordinance.category.name }}
                                </span>
                            {% else %}
                                <span class="text-gray-400">No category</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div id="status-badge-{{ ordinance.id }}">
                                {% include 'ordinances/admin/partials/status_badge.html' %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center">
                            {% if ordinance.has_pdf %}
                                <div class="flex items-center justify-center">
                                    <svg class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                    <span class="sr-only">Has PDF</span>
                                </div>
                            {% else %}
                                <span class="text-gray-300">—</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ ordinance.year_passed }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div>{{ ordinance.created_at|date:"M d, Y" }}</div>
                            {% if ordinance.updated_at != ordinance.created_at %}
                                <div class="text-xs text-gray-400">
                                    Updated {{ ordinance.updated_at|date:"M d" }}
                                </div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center justify-end space-x-2">
                                <!-- Status Update Dropdown -->
                                <div class="relative" x-data="{ open: false }">
                                    <button @click="open = !open"
                                            class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                  d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                        </svg>
                                    </button>

                                    <div x-show="open" @click.away="open = false" x-cloak
                                         class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                        <div class="py-1">
                                            {% for value, label in status_choices %}
                                                {% if value != ordinance.status %}
                                                    <button hx-post="{% url 'ordinances:update_ordinance_status' ordinance.id %}"
                                                            hx-vals='{"status": "{{ value }}"}'
                                                            hx-target="#status-badge-{{ ordinance.id }}"
                                                            hx-swap="innerHTML"
                                                            @click="open = false"
                                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                        Change to {{ label }}
                                                    </button>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>

                                <a href="{% url 'ordinances:admin_ordinance_edit' ordinance.id %}"
                                   class="text-blue-600 hover:text-blue-900 font-medium">
                                    Edit
                                </a>

                                {% if ordinance.is_public %}
                                    <a href="{{ ordinance.get_absolute_url }}"
                                       target="_blank"
                                       class="text-green-600 hover:text-green-900 font-medium">
                                        View
                                    </a>
                                {% endif %}

                                {% if ordinance.has_pdf %}
                                    <a href="{% url 'ordinances:secure_pdf_viewer' ordinance.slug %}"
                                       class="text-red-600 hover:text-red-900 font-medium">
                                        View PDF
                                    </a>
                                {% endif %}

                                <a href="{% url 'ordinances:export_pdf' ordinance.slug %}"
                                   class="text-purple-600 hover:text-purple-900 font-medium">
                                    {% if ordinance.has_pdf %}Gen PDF{% else %}PDF{% endif %}
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg">
            <div class="flex flex-1 justify-between sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                       hx-get="{% url 'ordinances:admin_ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                       hx-target="#admin-ordinance-results"
                       hx-push-url="true"
                       class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Previous
                    </a>
                {% endif %}

                {% if page_obj.has_next %}
                    <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                       hx-get="{% url 'ordinances:admin_ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                       hx-target="#admin-ordinance-results"
                       hx-push-url="true"
                       class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Next
                    </a>
                {% endif %}
            </div>

            <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </p>
                </div>

                <div>
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                        {% if page_obj.has_previous %}
                            <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                               hx-get="{% url 'ordinances:admin_ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.previous_page_number }}"
                               hx-target="#admin-ordinance-results"
                               hx-push-url="true"
                               class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span class="relative z-10 inline-flex items-center bg-blue-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                                    {{ num }}
                                </span>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ num }}"
                                   hx-get="{% url 'ordinances:admin_ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ num }}"
                                   hx-target="#admin-ordinance-results"
                                   hx-push-url="true"
                                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <a href="?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                               hx-get="{% url 'ordinances:admin_ordinance_list' %}?{% if search_query %}search={{ search_query }}&{% endif %}{% if selected_category %}category={{ selected_category }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}page={{ page_obj.next_page_number }}"
                               hx-target="#admin-ordinance-results"
                               hx-push-url="true"
                               class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
        </div>
    {% endif %}

{% else %}
    <!-- No Results -->
    <div class="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="text-gray-400 mb-4">
            <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No ordinances found</h3>
        <p class="text-gray-600 mb-4">Try adjusting your search criteria or create a new ordinance.</p>
        <div class="space-x-4">
            <a href="{% url 'ordinances:admin_ordinance_list' %}"
               class="text-blue-600 hover:text-blue-800 font-medium">
                Clear all filters
            </a>
            <a href="{% url 'ordinances:admin_ordinance_create' %}"
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Create Ordinance
            </a>
        </div>
    </div>
{% endif %}
