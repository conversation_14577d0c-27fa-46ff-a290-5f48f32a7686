from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.files.uploadedfile import SimpleUploadedFile
from ordinances.models import Ordinance, Category, Sponsor
import tempfile
import os

User = get_user_model()


class SecurePdfViewerTestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = Client()

        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test category
        self.category = Category.objects.create(
            name='Test Category',
            slug='test-category'
        )

        # Create test sponsor
        self.sponsor = Sponsor.objects.create(
            name='Test Sponsor',
            position='Councilor'
        )

        # Create test ordinance without PDF
        self.ordinance_no_pdf = Ordinance.objects.create(
            ordinance_number='ORD-TEST-001',
            title='Test Ordinance Without PDF',
            content='This is a test ordinance without PDF.',
            year_passed=2024,
            status='published',
            category=self.category,
            created_by=self.user,
            updated_by=self.user
        )

        # Create a simple PDF file for testing
        self.pdf_content = b'%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF'

        # Create test ordinance with PDF
        pdf_file = SimpleUploadedFile(
            "test.pdf",
            self.pdf_content,
            content_type="application/pdf"
        )

        self.ordinance_with_pdf = Ordinance.objects.create(
            ordinance_number='ORD-TEST-002',
            title='Test Ordinance With PDF',
            content='This is a test ordinance with PDF.',
            year_passed=2024,
            status='published',
            category=self.category,
            created_by=self.user,
            updated_by=self.user,
            pdf_file=pdf_file
        )

    def test_secure_pdf_viewer_url_exists(self):
        """Test that the secure PDF viewer URL exists"""
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)

    def test_secure_pdf_viewer_without_pdf_redirects(self):
        """Test that accessing viewer for ordinance without PDF redirects"""
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_no_pdf.slug})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)  # Should redirect

    def test_secure_pdf_viewer_context(self):
        """Test that the secure PDF viewer has correct context"""
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertIn('ordinance', response.context)
        self.assertIn('security_context', response.context)
        self.assertIn('pdf_url', response.context)

        # Check security context structure
        security_context = response.context['security_context']
        self.assertIn('user_ip', security_context)
        self.assertIn('access_time', security_context)
        self.assertIn('session_id', security_context)

    def test_secure_pdf_viewer_template_used(self):
        """Test that the correct template is used"""
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ordinances/secure_pdf_viewer.html')

    def test_secure_pdf_viewer_with_authenticated_user(self):
        """Test secure PDF viewer with authenticated user"""
        self.client.login(username='testuser', password='testpass123')
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'testuser')  # Should show username in watermark

    def test_secure_pdf_viewer_with_anonymous_user(self):
        """Test secure PDF viewer with anonymous user"""
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Guest')  # Should show Guest in watermark

    def test_secure_pdf_viewer_security_features(self):
        """Test that security features are present in the template"""
        url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        # Check for security notices
        self.assertContains(response, 'CONFIDENTIAL')
        self.assertContains(response, 'Document Security Notice')
        self.assertContains(response, 'Unauthorized reproduction')

        # Check for watermark elements
        self.assertContains(response, 'watermark-overlay')
        self.assertContains(response, 'OFFICIAL DOCUMENT')

        # Check for PDF.js implementation elements
        self.assertContains(response, 'pdf-canvas')
        self.assertContains(response, 'pdf-controls')
        self.assertContains(response, 'PDF.js')

    def test_ordinance_detail_view_updated(self):
        """Test that ordinance detail view shows the new secure viewer link"""
        url = reverse('ordinances:ordinance_detail', kwargs={'slug': self.ordinance_with_pdf.slug})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        # Should contain link to secure PDF viewer
        secure_viewer_url = reverse('ordinances:secure_pdf_viewer', kwargs={'slug': self.ordinance_with_pdf.slug})
        self.assertContains(response, secure_viewer_url)
        self.assertContains(response, 'View Official PDF')

    def tearDown(self):
        """Clean up test files"""
        # Clean up any uploaded files
        if self.ordinance_with_pdf.pdf_file:
            if os.path.exists(self.ordinance_with_pdf.pdf_file.path):
                os.remove(self.ordinance_with_pdf.pdf_file.path)
