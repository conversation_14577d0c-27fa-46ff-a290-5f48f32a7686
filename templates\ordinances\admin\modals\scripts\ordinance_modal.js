/**
 * Ordinance Modal JavaScript
 * Handles the Add Ordinance modal functionality
 */

// Ordinance Modal Functions
window.openAddOrdinanceModal = function() {
    openModal('addOrdinanceModal', 'ordinanceModalContent');
};

window.closeAddOrdinanceModal = function() {
    closeModal('addOrdinanceModal', 'ordinanceModalContent', 'addOrdinanceForm');
};

// Form validation
function validateOrdinanceForm() {
    const ordinanceNumber = document.getElementById('ordinance_number').value.trim();
    const title = document.getElementById('title').value.trim();
    const content = document.getElementById('content').value.trim();
    const yearPassed = document.getElementById('year_passed').value;

    if (!ordinanceNumber || !title || !content || !yearPassed) {
        showNotification('Please fill in all required fields.', 'error');
        return false;
    }

    return true;
}

// Custom form submission for ordinance (with validation)
function setupOrdinanceFormSubmission() {
    const form = document.getElementById('addOrdinanceForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate form first
        if (!validateOrdinanceForm()) {
            return;
        }

        const submitBtn = document.getElementById('ordinanceSubmitBtn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = `
            <svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
        `;

        // Submit form via AJAX
        const formData = new FormData(form);

        fetch('/manage/ordinances/create-ajax/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Ordinance created successfully!', 'success');
                closeAddOrdinanceModal();
                // Refresh the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error creating ordinance.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
}

// PDF Upload functionality
function initializePdfUpload() {
    const uploadArea = document.getElementById('pdfUploadArea');
    const fileInput = document.getElementById('pdfFile');
    const pdfPreview = document.getElementById('pdfPreview');
    const pdfFileName = document.getElementById('pdfFileName');
    const pdfFileSize = document.getElementById('pdfFileSize');
    const removePdfBtn = document.getElementById('removePdf');
    const uploadHint = document.getElementById('pdfUploadHint');

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // File input change
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handlePdfFile(file);
        }
    });

    // Drag and drop
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (file.type === 'application/pdf') {
                // Create a new FileList-like object
                const dt = new DataTransfer();
                dt.items.add(file);
                fileInput.files = dt.files;
                handlePdfFile(file);
            } else {
                showNotification('Please select a PDF file only.', 'error');
            }
        }
    });

    // Remove PDF
    removePdfBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        removePdf();
    });

    function handlePdfFile(file) {
        // Validate file type
        if (file.type !== 'application/pdf') {
            showNotification('Please select a PDF file only.', 'error');
            return;
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
            showNotification('File size cannot exceed 10MB.', 'error');
            return;
        }

        // Show loading state
        uploadArea.classList.add('uploading');

        // Update preview
        pdfFileName.textContent = file.name;
        pdfFileSize.textContent = formatFileSize(file.size);

        // Show preview and hide upload area
        setTimeout(() => {
            uploadArea.classList.remove('uploading');
            uploadArea.classList.add('success');
            uploadArea.style.display = 'none';
            pdfPreview.classList.remove('hidden');

            // Hide the hint indicator
            if (uploadHint) {
                uploadHint.style.display = 'none';
            }
        }, 500);
    }

    function removePdf() {
        fileInput.value = '';
        pdfPreview.classList.add('hidden');
        uploadArea.style.display = 'flex';
        uploadArea.classList.remove('success', 'uploading');

        // Show the hint indicator
        if (uploadHint) {
            uploadHint.style.display = 'flex';
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize ordinance modal functionality
function initializeOrdinanceModal() {
    // Setup click outside to close
    setupClickOutsideClose('addOrdinanceModal', closeAddOrdinanceModal);

    // Initialize PDF upload functionality
    initializePdfUpload();

    // Setup custom form submission with validation
    setupOrdinanceFormSubmission();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeOrdinanceModal();
});
