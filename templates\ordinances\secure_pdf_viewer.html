{% extends 'layouts/government.html' %}

{% block title %}{{ ordinance.title }} - PDF Viewer - Sangguniang Bayan Ordinance System{% endblock %}

{% block extra_css %}
<style>
    #pdf-canvas {
        border: 1px solid #ccc;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .pdf-controls {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 10px;
    }
    .pdf-controls button {
        margin: 0 2px;
        padding: 4px 8px;
        border: 1px solid #ccc;
        background: white;
        border-radius: 3px;
        cursor: pointer;
    }
    .pdf-controls button:hover {
        background: #e9ecef;
    }
    .pdf-controls button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'ordinances:home' %}" class="text-gray-700 hover:text-blue-600">
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{% url 'ordinances:ordinance_list' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                        Ordinances
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ ordinance.get_absolute_url }}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                        {{ ordinance.ordinance_number }}
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">PDF Viewer</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-900 mb-2">{{ ordinance.title }}</h1>
                <div class="flex items-center gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded">
                        {{ ordinance.ordinance_number }}
                    </span>
                    <span class="bg-gray-100 text-gray-800 text-sm font-semibold px-3 py-1 rounded">
                        {{ ordinance.year_passed }}
                    </span>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-3 mt-4 lg:mt-0">
                <a href="{{ ordinance.get_absolute_url }}"
                   class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18" />
                    </svg>
                    Back to Details
                </a>

                <button onclick="window.print()"
                        class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    Print
                </button>
            </div>
        </div>
    </div>

    <!-- Security Notice and Watermark -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <svg class="h-5 w-5 text-red-400 mt-0.5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div class="flex-1">
                <h3 class="text-sm font-medium text-red-800 mb-1">Document Security Notice</h3>
                <div class="text-sm text-red-700">
                    <p class="mb-2"><strong>This document is protected.</strong> Unauthorized reproduction, screenshots, or distribution is prohibited and may be subject to legal action.</p>
                    <div class="bg-red-100 p-3 rounded border text-xs font-mono">
                        {% if user.is_authenticated %}
                            {{ user.username }} - {{ user.email }} - {{ security_context.user_ip }} - {{ security_context.access_time }} - Session: {{ security_context.session_id|slice:":8" }}
                        {% else %}
                            Guest - {{ security_context.user_ip }} - {{ security_context.access_time }} - Session: {{ security_context.session_id|slice:":8" }}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Viewer Container -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="bg-gray-50 px-6 py-3 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium text-gray-900">Official Document</h2>
                <div id="pdf-status" class="text-sm text-gray-600">
                    <span id="loading-indicator" class="inline-flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Loading PDF...
                    </span>
                    <span id="success-indicator" class="hidden text-green-600">✓ PDF Loaded</span>
                    <span id="error-indicator" class="hidden text-red-600">⚠ Loading Failed</span>
                </div>
            </div>
        </div>

        <div class="relative">
            <!-- Debug Information -->
            <div id="debug-info" class="bg-blue-50 border border-blue-200 rounded p-3 mb-4 text-sm">
                <strong>Debug Info:</strong><br>
                PDF URL: <a href="{{ pdf_url }}" target="_blank" class="text-blue-600 underline">{{ pdf_url }}</a><br>
                File Path: {{ pdf_file_path }}<br>
                File Size: {{ pdf_file_size|filesizeformat }}<br>
                Browser: <span id="browser-info"></span><br>
                PDF.js Status: <span id="pdfjs-status">Loading...</span><br>
                <button onclick="testDirectPdfAccess()" class="mt-2 px-2 py-1 bg-blue-500 text-white rounded text-xs">Test Direct PDF Access</button>
            </div>

            <!-- PDF.js Controls -->
            <div class="pdf-controls">
                <button id="prev-page" onclick="prevPage()">← Previous</button>
                <span>Page: <span id="page-num">1</span> / <span id="page-count">-</span></span>
                <button id="next-page" onclick="nextPage()">Next →</button>
                <button onclick="zoomIn()">Zoom In</button>
                <button onclick="zoomOut()">Zoom Out</button>
                <button onclick="resetZoom()">Reset Zoom</button>
                <span class="ml-4">Scale: <span id="zoom-level">100%</span></span>
            </div>

            <!-- PDF.js Canvas Container -->
            <div id="pdf-container" class="w-full text-center" style="height: 80vh; overflow: auto;">
                <canvas id="pdf-canvas" style="max-width: 100%;"></canvas>
            </div>

            <!-- Fallback for PDF loading errors -->
            <div id="pdf-fallback" class="hidden p-8 text-center">
                <div class="mb-4">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">PDF Loading Failed</h3>
                <p class="text-gray-600 mb-4">Unable to load the PDF document.</p>
                <a href="{{ pdf_url }}"
                   target="_blank"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    Open PDF in New Tab
                </a>
            </div>
        </div>

            <!-- Overlay watermark -->
            <div id="watermark-overlay" class="absolute inset-0 pointer-events-none z-10" style="background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 100px,
                rgba(239, 68, 68, 0.05) 100px,
                rgba(239, 68, 68, 0.05) 120px
            );">
                <div class="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 rounded text-xs font-bold opacity-75 shadow-lg">
                    CONFIDENTIAL
                </div>
                <div class="absolute bottom-4 left-4 bg-red-600 text-white px-3 py-1 rounded text-xs font-bold opacity-75 shadow-lg">
                    OFFICIAL DOCUMENT
                </div>

                <!-- Center watermark for extra visibility -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -rotate-45 opacity-10 pointer-events-none">
                    <div class="text-red-600 text-6xl font-bold whitespace-nowrap">
                        CONFIDENTIAL DOCUMENT
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Security Information -->
    <div class="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-900 mb-2">Access Information</h3>
        <div class="text-xs text-gray-600 space-y-1">
            <p><strong>Document:</strong> {{ ordinance.ordinance_number }} - {{ ordinance.title }}</p>
            <p><strong>Accessed by:</strong>
                {% if user.is_authenticated %}
                    {{ user.get_full_name|default:user.username }} ({{ user.email }})
                {% else %}
                    Guest User
                {% endif %}
            </p>
            <p><strong>Access Time:</strong> {{ security_context.access_time }}</p>
            <p><strong>IP Address:</strong> {{ security_context.user_ip }}</p>
            <p><strong>Session ID:</strong> {{ security_context.session_id|slice:":8" }}...</p>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<!-- PDF.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>

<script>
    // PDF.js variables
    let pdfDoc = null;
    let pageNum = 1;
    let pageRendering = false;
    let pageNumPending = null;
    let scale = 1.0;
    const canvas = document.getElementById('pdf-canvas');
    const ctx = canvas.getContext('2d');

    // PDF URL from Django template
    const pdfUrl = '{{ pdf_url }}';

    // Disable right-click context menu
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable text selection
    document.addEventListener('selectstart', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable drag and drop
    document.addEventListener('dragstart', function(e) {
        e.preventDefault();
        return false;
    });

    // Disable certain keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A
        if (e.keyCode === 123 ||
            (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
            (e.ctrlKey && (e.keyCode === 85 || e.keyCode === 83 || e.keyCode === 65))) {
            e.preventDefault();
            return false;
        }
    });

    // Update status indicators
    function updateStatus(status, message = '') {
        const statusElement = document.getElementById('pdfjs-status');
        const loading = document.getElementById('loading-indicator');
        const success = document.getElementById('success-indicator');
        const error = document.getElementById('error-indicator');

        if (loading) loading.classList.add('hidden');
        if (success) success.classList.add('hidden');
        if (error) error.classList.add('hidden');

        if (status === 'loading') {
            statusElement.textContent = 'Loading PDF...';
            if (loading) loading.classList.remove('hidden');
        } else if (status === 'success') {
            statusElement.textContent = 'PDF Loaded Successfully';
            if (success) success.classList.remove('hidden');
        } else if (status === 'error') {
            statusElement.textContent = 'Error: ' + message;
            if (error) error.classList.remove('hidden');
        }
    }

    // Render a page of the PDF
    function renderPage(num) {
        pageRendering = true;
        updateStatus('loading');

        // Get page
        pdfDoc.getPage(num).then(function(page) {
            const viewport = page.getViewport({scale: scale});
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            // Render PDF page into canvas context
            const renderContext = {
                canvasContext: ctx,
                viewport: viewport
            };

            const renderTask = page.render(renderContext);

            // Wait for rendering to finish
            renderTask.promise.then(function() {
                pageRendering = false;
                updateStatus('success');

                if (pageNumPending !== null) {
                    // New page rendering is pending
                    renderPage(pageNumPending);
                    pageNumPending = null;
                }
            });
        });

        // Update page counters
        document.getElementById('page-num').textContent = num;
    }

    // Queue rendering of a page
    function queueRenderPage(num) {
        if (pageRendering) {
            pageNumPending = num;
        } else {
            renderPage(num);
        }
    }

    // Previous page
    function prevPage() {
        if (pageNum <= 1) {
            return;
        }
        pageNum--;
        queueRenderPage(pageNum);
        updatePageButtons();
    }

    // Next page
    function nextPage() {
        if (pageNum >= pdfDoc.numPages) {
            return;
        }
        pageNum++;
        queueRenderPage(pageNum);
        updatePageButtons();
    }

    // Update page navigation buttons
    function updatePageButtons() {
        document.getElementById('prev-page').disabled = (pageNum <= 1);
        document.getElementById('next-page').disabled = (pageNum >= pdfDoc.numPages);
    }

    // Zoom functions
    function zoomIn() {
        scale += 0.25;
        updateZoomLevel();
        queueRenderPage(pageNum);
    }

    function zoomOut() {
        if (scale > 0.25) {
            scale -= 0.25;
            updateZoomLevel();
            queueRenderPage(pageNum);
        }
    }

    function resetZoom() {
        scale = 1.0;
        updateZoomLevel();
        queueRenderPage(pageNum);
    }

    function updateZoomLevel() {
        document.getElementById('zoom-level').textContent = Math.round(scale * 100) + '%';
    }

    // Load and render PDF
    function loadPDF() {
        updateStatus('loading');

        // Set PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';

        // Load PDF
        pdfjsLib.getDocument(pdfUrl).promise.then(function(pdfDoc_) {
            pdfDoc = pdfDoc_;
            document.getElementById('page-count').textContent = pdfDoc.numPages;

            // Initial page render
            renderPage(pageNum);
            updatePageButtons();
            updateZoomLevel();

            console.log('PDF loaded successfully');
        }).catch(function(error) {
            console.error('Error loading PDF:', error);
            updateStatus('error', error.message);
            showPdfFallback();
        });
    }

    // Show PDF fallback
    function showPdfFallback() {
        document.getElementById('pdf-container').style.display = 'none';
        document.getElementById('pdf-fallback').classList.remove('hidden');
        document.getElementById('watermark-overlay').style.display = 'none';
    }

    // Test direct PDF access
    function testDirectPdfAccess() {
        fetch(pdfUrl)
            .then(response => {
                if (response.ok) {
                    console.log('PDF accessible:', response.status, response.headers.get('content-type'));
                    alert('PDF is accessible. Status: ' + response.status + ', Type: ' + response.headers.get('content-type'));
                } else {
                    console.log('PDF not accessible:', response.status);
                    alert('PDF not accessible. Status: ' + response.status);
                }
            })
            .catch(error => {
                console.log('Error accessing PDF:', error);
                alert('Error accessing PDF: ' + error.message);
            });
    }

    // Detect browser capabilities
    function detectBrowserCapabilities() {
        const userAgent = navigator.userAgent;
        let browserName = 'Unknown';

        if (userAgent.indexOf('Chrome') > -1) {
            browserName = 'Chrome';
        } else if (userAgent.indexOf('Firefox') > -1) {
            browserName = 'Firefox';
        } else if (userAgent.indexOf('Safari') > -1) {
            browserName = 'Safari';
        } else if (userAgent.indexOf('Edge') > -1) {
            browserName = 'Edge';
        }

        document.getElementById('browser-info').textContent = browserName;
        console.log('Browser:', browserName);
    }

    // Initialize when page loads
    window.addEventListener('load', function() {
        detectBrowserCapabilities();
        loadPDF();
    });
</script>
{% endblock %}
