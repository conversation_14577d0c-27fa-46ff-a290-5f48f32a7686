# 📐 Government Website Layout System

This directory contains specialized layout templates optimized for government websites, focusing on **readability**, **professionalism**, and **trustworthiness** - essential for legal document platforms.

## 🏛️ **Government Website Best Practices**

### **✅ Limited Width for Content (Recommended)**
- **Legal documents** need optimal readability
- **600-900px max width** prevents eye strain
- **Professional appearance** builds trust
- **Focus on content** over visual effects

### **✅ Full Width Usage (Selective)**
- **Hero/Banner sections** only
- **Navigation bar/header**
- **Search sections**
- **Background elements**

## 🎯 **Layout Templates Overview**

### **1. `government.html` - Government Layout** ⭐ **RECOMMENDED**
**Purpose**: Professional layout optimized for official government content.

**Best For**:
- Legal documents and ordinances
- Official announcements
- Policy documents
- Formal government content

**Width Configuration**:
- **Content Width**: 768px (max-w-3xl) - Perfect for legal text
- **Line Length**: 60-70 characters (optimal for legal documents)
- **Background**: Light gray for professional appearance
- **Padding**: Generous spacing for formal presentation

**Usage**:
```html
{% extends 'layouts/government.html' %}
```

**Example**: Ordinance detail pages, official documents

---

### **2. `reading.html` - Reading Layout**
**Purpose**: Optimized for text readability with proper line length.

**Best For**:
- Long-form articles
- Documentation pages
- Educational content
- News articles

**Width Configuration**:
- **Max Width**: 768px (max-w-3xl) for optimal reading
- **Line Length**: 60-70 characters per line
- **Padding**: Comfortable margins for focus

**Usage**:
```html
{% extends 'layouts/reading.html' %}
```

**Example**: Blog posts, help documentation

---

### **3. `wide.html` - Wide Layout** ⚠️ **USE SPARINGLY**
**Purpose**: Moderate width increase for data-heavy content.

**Best For**:
- Data tables (when necessary)
- Admin interfaces
- Dashboard content

**Width Configuration**:
- **Desktop**: Controlled width expansion
- **Tablet**: Responsive adjustments
- **Mobile**: Standard mobile-friendly

**Usage**:
```html
{% extends 'layouts/wide.html' %}
```

**Example**: Admin dashboards only

---

### **4. `full_width.html` - Full Width Layout** ⚠️ **AVOID FOR CONTENT**
**Purpose**: Maximum width for background elements only.

**Best For**:
- Hero sections with background images
- Full-width navigation
- Marketing landing pages (non-government)

**Usage**:
```html
{% extends 'layouts/full_width.html' %}
```

**Example**: Home page with contained content sections

---

## 🏗️ **Base Template Architecture**

### **Flexible Container System**
The base template now includes flexible container blocks:

```html
<main class="flex-1 {% block main_classes %}{% endblock %}">
    <div class="{% block container_classes %}max-w-7xl mx-auto px-4 sm:px-6 lg:px-8{% endblock %}">
        {% block content %}
        {% endblock %}
    </div>
</main>
```

### **Customizable Blocks**:
- **`main_classes`**: Add classes to the main element
- **`container_classes`**: Override container width and padding
- **`content`**: Your page content

---

## 📱 **Responsive Design Principles**

### **Mobile-First Approach**
All layouts follow mobile-first responsive design:

1. **Base**: Mobile-optimized by default
2. **sm**: Small tablets (640px+)
3. **md**: Tablets (768px+)
4. **lg**: Small desktops (1024px+)
5. **xl**: Large desktops (1280px+)
6. **2xl**: Extra large screens (1536px+)

### **Padding Scale**:
```css
px-4     /* 16px - Mobile */
sm:px-6  /* 24px - Small tablets */
lg:px-8  /* 32px - Desktops */
xl:px-12 /* 48px - Large screens */
2xl:px-16 /* 64px - Extra large */
```

---

## 🎨 **Layout Selection Guide**

### **Choose `full_width.html` when**:
- ✅ Content needs maximum screen real estate
- ✅ Building landing pages or marketing content
- ✅ Using full-width components (heroes, galleries)
- ✅ Content manages its own responsive behavior

### **Choose `wide.html` when**:
- ✅ Displaying data tables or lists
- ✅ Building admin interfaces or dashboards
- ✅ Content benefits from horizontal space
- ✅ Multiple columns or complex layouts

### **Choose `reading.html` when**:
- ✅ Primary content is text-based
- ✅ Optimizing for reading experience
- ✅ Displaying articles or documents
- ✅ Content length is substantial

### **Choose `base.html` when**:
- ✅ Standard content that fits well in containers
- ✅ Balanced layout needs
- ✅ Default responsive behavior is sufficient

---

## 🔧 **Custom Layout Creation**

### **Creating a New Layout**:

1. **Create the layout file**:
```html
{% extends 'base.html' %}

{# Custom Layout Description #}

{% block container_classes %}your-custom-classes{% endblock %}
{% block main_classes %}your-main-classes{% endblock %}
```

2. **Use in your template**:
```html
{% extends 'layouts/your_custom_layout.html' %}
```

### **Common Patterns**:

**Sidebar Layout**:
```html
{% block container_classes %}max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex gap-8{% endblock %}
```

**Centered Narrow**:
```html
{% block container_classes %}max-w-2xl mx-auto px-4 sm:px-6{% endblock %}
```

**Admin Interface**:
```html
{% block container_classes %}max-w-full mx-auto px-6 lg:px-12{% endblock %}
{% block main_classes %}bg-gray-50{% endblock %}
```

---

## 📊 **Performance Considerations**

### **Width Optimization Benefits**:
- **Better Readability**: Optimal line lengths improve reading speed
- **Improved UX**: Content fits naturally on different screen sizes
- **Reduced Cognitive Load**: Proper spacing and width reduce eye strain
- **Better Engagement**: Users stay longer on well-formatted content

### **SEO Benefits**:
- **Mobile-First**: Google prioritizes mobile-friendly layouts
- **Reading Experience**: Better user metrics (time on page, bounce rate)
- **Accessibility**: Proper line lengths help users with reading difficulties

---

## 🚀 **Future Enhancements**

- **Print Layouts**: Specialized layouts for print media
- **Email Templates**: Layouts optimized for email content
- **Component Layouts**: Layouts for specific component types
- **Theme Variations**: Dark mode and theme-specific layouts
