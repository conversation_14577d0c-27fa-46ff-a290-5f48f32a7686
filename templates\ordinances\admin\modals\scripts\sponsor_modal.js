/**
 * Sponsor Modal JavaScript
 * Handles the Add Sponsor modal functionality
 */

// Sponsor Modal Functions
window.openAddSponsorModal = function() {
    openModal('addSponsorModal', 'sponsorModalContent');
};

window.closeAddSponsorModal = function() {
    closeModal('addSponsorModal', 'sponsorModalContent', 'addSponsorForm');
};

// Initialize sponsor modal functionality
function initializeSponsorModal() {
    // Setup click outside to close
    setupClickOutsideClose('addSponsorModal', closeAddSponsorModal);

    // Setup form submission
    handleFormSubmission(
        'addSponsorForm',
        '/manage/sponsors/create-ajax/',
        'Sponsor created successfully!',
        closeAddSponsorModal
    );
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeSponsorModal();
});
