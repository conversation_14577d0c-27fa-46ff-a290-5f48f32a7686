{% extends 'layouts/government.html' %}

{% block title %}{{ ordinance.title }} - Sangguniang Bayan Ordinance System{% endblock %}

{% block content %}
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'ordinances:home' %}" class="text-gray-700 hover:text-blue-600">
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{% url 'ordinances:ordinance_list' %}" class="ml-1 text-gray-700 hover:text-blue-600 md:ml-2">
                        Ordinances
                    </a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">{{ ordinance.ordinance_number }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-6">
            <div class="flex-1">
                <div class="flex items-center gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-3 py-1 rounded">
                        {{ ordinance.ordinance_number }}
                    </span>
                    <span class="bg-gray-100 text-gray-800 text-sm font-semibold px-3 py-1 rounded">
                        {{ ordinance.year_passed }}
                    </span>
                    {% if ordinance.status == 'published' %}
                        <span class="bg-green-100 text-green-800 text-sm font-semibold px-3 py-1 rounded">
                            Published
                        </span>
                    {% elif ordinance.status == 'approved' %}
                        <span class="bg-yellow-100 text-yellow-800 text-sm font-semibold px-3 py-1 rounded">
                            Approved
                        </span>
                    {% endif %}
                </div>

                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ ordinance.title }}</h1>

                <!-- Metadata -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                    {% if ordinance.category %}
                        <div class="flex items-center">
                            <svg class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                            <span><strong>Category:</strong> {{ ordinance.category.name }}</span>
                        </div>
                    {% endif %}

                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm6-8a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <span><strong>Status:</strong> {{ ordinance.get_status_display }}</span>
                    </div>

                    <div class="flex items-center">
                        <svg class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span><strong>Created:</strong> {{ ordinance.created_at|date:"F d, Y" }}</span>
                    </div>

                    {% if ordinance.updated_at != ordinance.created_at %}
                        <div class="flex items-center">
                            <svg class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            <span><strong>Updated:</strong> {{ ordinance.updated_at|date:"F d, Y" }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-6 lg:mt-0 lg:ml-8 flex flex-col sm:flex-row lg:flex-col gap-3">
                {% if ordinance.has_pdf %}
                    <!-- Secure PDF Viewer -->
                    <a href="{% url 'ordinances:secure_pdf_viewer' ordinance.slug %}"
                       class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-red-600 hover:bg-red-700 transition-colors">
                        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View Official PDF
                    </a>
                {% endif %}

                <!-- Generated PDF Download -->
                <a href="{% url 'ordinances:export_pdf' ordinance.slug %}"
                   class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                    <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {% if ordinance.has_pdf %}Generate PDF{% else %}Download PDF{% endif %}
                </a>

                <button onclick="window.print()"
                        class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                    </svg>
                    Print
                </button>

                <button onclick="navigator.share ? navigator.share({title: '{{ ordinance.title }}', url: window.location.href}) : copyToClipboard(window.location.href)"
                        class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    Share
                </button>
            </div>
        </div>

        <!-- Sponsors -->
        {% if ordinance.sponsors.exists %}
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-3">Sponsors</h3>
                <div class="flex flex-wrap gap-3">
                    {% for sponsor in ordinance.sponsors.all %}
                        <div class="bg-gray-50 rounded-lg px-4 py-2">
                            <div class="font-medium text-gray-900">{{ sponsor.name }}</div>
                            <div class="text-sm text-gray-600">{{ sponsor.position }}</div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Content -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
        <h2 class="text-2xl font-bold text-gray-900 mb-6">Ordinance Content</h2>
        <div class="prose prose-lg max-w-none">
            {{ ordinance.content|linebreaks }}
        </div>
    </div>

    <!-- Attachments -->
    {% if ordinance.attachments.exists %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Attachments</h2>
            <div class="space-y-4">
                {% for attachment in ordinance.attachments.all %}
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center">
                            <svg class="h-8 w-8 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                            </svg>
                            <div>
                                <div class="font-medium text-gray-900">
                                    {{ attachment.file.name|default:"Attachment" }}
                                </div>
                                {% if attachment.description %}
                                    <div class="text-sm text-gray-600">{{ attachment.description }}</div>
                                {% endif %}
                                <div class="text-xs text-gray-500">
                                    Uploaded {{ attachment.uploaded_at|date:"M d, Y" }}
                                </div>
                            </div>
                        </div>
                        <a href="{{ attachment.file.url }}"
                           target="_blank"
                           class="text-blue-600 hover:text-blue-800 font-medium">
                            Download
                        </a>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    <!-- Related Ordinances -->
    {% if related_ordinances %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6">Related Ordinances</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {% for related in related_ordinances %}
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center gap-2 mb-2">
                            <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-0.5 rounded">
                                {{ related.ordinance_number }}
                            </span>
                            <span class="text-xs text-gray-500">{{ related.year_passed }}</span>
                        </div>
                        <h3 class="font-medium text-gray-900 mb-2 line-clamp-2">
                            {{ related.title }}
                        </h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                            {{ related.content|truncatewords:15 }}
                        </p>
                        <a href="{{ related.get_absolute_url }}"
                           class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            Read More →
                        </a>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show a temporary notification
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            notification.textContent = 'Link copied to clipboard!';
            document.body.appendChild(notification);

            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        });
    }
</script>
{% endblock %}
{% endblock %}
