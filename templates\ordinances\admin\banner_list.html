{% extends 'base.html' %}

{% block title %}Manage Banners - Admin{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-4">
                <i class="fas fa-bullhorn text-blue-600 mr-3"></i>
                Manage Banners
            </h1>
            <p class="text-lg text-gray-600">Create and manage announcement banners for the website</p>
        </div>
        <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-3">
            <a href="{% url 'ordinances:admin_dashboard' %}" 
               class="bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
            </a>
            <a href="{% url 'ordinances:admin_banner_create' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Add New Banner
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <form method="get" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" name="search" value="{{ search_query }}" 
                           placeholder="Search banners..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                    <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">All Types</option>
                        {% for value, label in banner_types %}
                            <option value="{{ value }}" {% if type_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Banners List -->
    {% if banners %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Banner</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Schedule</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for banner in banners %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-start space-x-3">
                                    {% if banner.show_icon %}
                                        <i class="{{ banner.get_icon_class }} text-lg mt-1"></i>
                                    {% endif %}
                                    <div class="flex-1 min-w-0">
                                        <div class="text-sm font-medium text-gray-900 truncate">{{ banner.title }}</div>
                                        <div class="text-sm text-gray-500 line-clamp-2">{{ banner.message|truncatewords:15 }}</div>
                                        <div class="mt-1">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ banner.get_css_classes }}">
                                                {{ banner.get_color_scheme_display }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    {{ banner.get_banner_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center space-x-2">
                                    {% if banner.is_currently_active %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-times-circle mr-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                    {% if banner.is_dismissible %}
                                        <span class="text-xs text-gray-500">Dismissible</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div>
                                    <div>Start: {{ banner.start_date|date:"M d, Y H:i" }}</div>
                                    {% if banner.end_date %}
                                        <div>End: {{ banner.end_date|date:"M d, Y H:i" }}</div>
                                    {% else %}
                                        <div class="text-gray-400">No end date</div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {{ banner.priority }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <a href="{% url 'ordinances:admin_banner_edit' banner.id %}" 
                                   class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </a>
                                <button onclick="toggleBanner({{ banner.id }})" 
                                        class="text-yellow-600 hover:text-yellow-900">
                                    <i class="fas fa-toggle-{% if banner.is_active %}on{% else %}off{% endif %} mr-1"></i>
                                    {% if banner.is_active %}Deactivate{% else %}Activate{% endif %}
                                </button>
                                <a href="{% url 'ordinances:admin_banner_delete' banner.id %}" 
                                   class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <i class="fas fa-bullhorn text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-bold text-gray-600 mb-2">No Banners Found</h3>
            <p class="text-gray-500 mb-6">
                {% if search_query or status_filter or type_filter %}
                    No banners match your current filters. Try adjusting your search criteria.
                {% else %}
                    You haven't created any banners yet. Create your first banner to get started.
                {% endif %}
            </p>
            <a href="{% url 'ordinances:admin_banner_create' %}" 
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                <i class="fas fa-plus mr-2"></i>Create First Banner
            </a>
        </div>
    {% endif %}
</div>

<script>
function toggleBanner(bannerId) {
    if (confirm('Are you sure you want to toggle this banner\'s status?')) {
        fetch(`/manage/banners/${bannerId}/toggle/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error toggling banner status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error toggling banner status');
        });
    }
}
</script>
{% endblock %}
