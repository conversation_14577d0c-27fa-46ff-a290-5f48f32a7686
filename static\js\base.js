// Base JavaScript for Sangguniang Bayan Ordinance System
document.addEventListener('DOMContentLoaded', function() {
    
    // Mobile menu functionality
    function initMobileMenu() {
        const mobileMenuButton = document.querySelector('[data-mobile-menu-button]');
        const mobileMenu = document.querySelector('[data-mobile-menu]');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                const isOpen = mobileMenu.classList.contains('show');
                if (isOpen) {
                    mobileMenu.classList.remove('show');
                    mobileMenuButton.setAttribute('aria-expanded', 'false');
                } else {
                    mobileMenu.classList.add('show');
                    mobileMenuButton.setAttribute('aria-expanded', 'true');
                }
            });
        }
    }

    // Message dismissal functionality
    function initMessageDismissal() {
        const messageCloseButtons = document.querySelectorAll('[data-dismiss="message"]');
        
        messageCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                const message = this.closest('.alert, .message');
                if (message) {
                    message.style.opacity = '0';
                    message.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        message.remove();
                    }, 300);
                }
            });
        });
    }

    // Form validation helpers
    function initFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');
                        
                        // Remove error styling on input
                        field.addEventListener('input', function() {
                            this.classList.remove('border-red-500');
                        }, { once: true });
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    // Show error message
                    showNotification('Please fill in all required fields.', 'error');
                }
            });
        });
    }

    // Notification system
    function showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
        
        // Apply color based on type
        switch(type) {
            case 'success':
                notification.classList.add('bg-green-500', 'text-white');
                break;
            case 'error':
                notification.classList.add('bg-red-500', 'text-white');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500', 'text-black');
                break;
            default:
                notification.classList.add('bg-primary-dark-blue', 'text-primary-offwhite');
        }
        
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button class="ml-4 text-lg font-bold" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, duration);
    }

    // Loading state management
    function setLoadingState(element, isLoading) {
        if (isLoading) {
            element.disabled = true;
            element.classList.add('opacity-50', 'cursor-not-allowed');
            
            // Add spinner if it's a button
            if (element.tagName === 'BUTTON') {
                const originalText = element.innerHTML;
                element.setAttribute('data-original-text', originalText);
                element.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
            }
        } else {
            element.disabled = false;
            element.classList.remove('opacity-50', 'cursor-not-allowed');
            
            // Restore original text if it's a button
            if (element.tagName === 'BUTTON' && element.hasAttribute('data-original-text')) {
                element.innerHTML = element.getAttribute('data-original-text');
                element.removeAttribute('data-original-text');
            }
        }
    }

    // HTMX integration helpers
    function initHTMXHelpers() {
        // Show loading indicator on HTMX requests
        document.addEventListener('htmx:beforeRequest', function(event) {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'block';
            }
            
            // Set loading state on the triggering element
            if (event.detail.elt) {
                setLoadingState(event.detail.elt, true);
            }
        });

        document.addEventListener('htmx:afterRequest', function(event) {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            // Remove loading state from the triggering element
            if (event.detail.elt) {
                setLoadingState(event.detail.elt, false);
            }
        });

        // Handle HTMX errors
        document.addEventListener('htmx:responseError', function(event) {
            showNotification('An error occurred. Please try again.', 'error');
        });
    }

    // Accessibility improvements
    function initAccessibility() {
        // Add keyboard navigation for custom dropdowns
        const dropdowns = document.querySelectorAll('[data-dropdown]');
        
        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // Add focus indicators for better accessibility
        const focusableElements = document.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        
        focusableElements.forEach(element => {
            element.addEventListener('focus', function() {
                this.classList.add('ring-2', 'ring-primary-dark-blue', 'ring-opacity-50');
            });
            
            element.addEventListener('blur', function() {
                this.classList.remove('ring-2', 'ring-primary-dark-blue', 'ring-opacity-50');
            });
        });
    }

    // Initialize all functionality
    initMobileMenu();
    initMessageDismissal();
    initFormValidation();
    initHTMXHelpers();
    initAccessibility();

    // Make functions available globally
    window.showNotification = showNotification;
    window.setLoadingState = setLoadingState;
});
