<!-- Add Ordinance Modal -->
<div id="addOrdinanceModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl max-w-5xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0" id="ordinanceModalContent">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-blue-600 to-cyan-600 text-white px-6 py-4 rounded-t-xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <svg class="h-6 w-6 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    <h2 class="text-xl font-bold">Add New Ordinance</h2>
                </div>
                <button onclick="closeAddOrdinanceModal()" class="text-white hover:text-gray-200 transition-colors">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <form id="addOrdinanceForm">
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Basic Information
                    </h3>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Ordinance Number -->
                        <div>
                            <label for="ordinance_number" class="block text-sm font-medium text-gray-700 mb-1">
                                Ordinance Number <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="ordinance_number" name="ordinance_number" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="e.g., ORD-2024-001">
                        </div>

                        <!-- Year Passed -->
                        <div>
                            <label for="year_passed" class="block text-sm font-medium text-gray-700 mb-1">
                                Year Passed <span class="text-red-500">*</span>
                            </label>
                            <select id="year_passed" name="year_passed" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">Select Year</option>
                                {% for year in years %}
                                    <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Title -->
                        <div class="lg:col-span-2">
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
                                Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="title" name="title" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Enter ordinance title">
                        </div>

                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                                Category
                            </label>
                            <select id="category" name="category"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">Select Category</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select id="status" name="status"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="draft">Draft</option>
                                <option value="reviewed">Under Review</option>
                                <option value="approved">Approved</option>
                                <option value="published">Published</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Sponsors Section -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-purple-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        Sponsors
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-300 rounded-lg p-4">
                        {% for sponsor in sponsors %}
                            <label class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 p-2 rounded">
                                <input type="checkbox" name="sponsors" value="{{ sponsor.id }}"
                                       class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <div class="flex-1 min-w-0">
                                    <div class="text-sm font-medium text-gray-900 truncate">{{ sponsor.name }}</div>
                                    <div class="text-xs text-gray-500">{{ sponsor.position }}</div>
                                </div>
                            </label>
                        {% empty %}
                            <p class="text-gray-500 text-sm col-span-full">No sponsors available.</p>
                        {% endfor %}
                    </div>
                </div>

                <!-- Content Section -->
                <div class="mb-6">
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-1">
                        Content <span class="text-red-500">*</span>
                    </label>
                    <textarea id="content" name="content" rows="8" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Enter the full ordinance content..."></textarea>
                    <p class="text-xs text-gray-500 mt-1">Enter the complete text of the ordinance with proper formatting.</p>
                </div>

                <!-- PDF Upload Section -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="h-5 w-5 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        PDF Document (Optional)
                    </h3>

                    <div class="pdf-upload-container">
                        <!-- File Upload Area -->
                        <div class="pdf-upload-area" id="pdfUploadArea">
                            <input type="file"
                                   name="pdf_file"
                                   id="pdfFile"
                                   accept=".pdf"
                                   class="hidden">

                            <!-- Visual Hint Indicator -->
                            <div class="upload-hint" id="pdfUploadHint">
                                <i class="fas fa-plus"></i>
                            </div>

                            <!-- Upload Icon with Animation -->
                            <div class="upload-icon">
                                <i class="fas fa-file-pdf text-red-500"></i>
                            </div>

                            <!-- Main Upload Text -->
                            <div class="upload-text">
                                Click to upload or drag & drop PDF
                            </div>

                            <!-- Subtext with file info -->
                            <div class="upload-subtext">
                                PDF files only, up to 10MB
                            </div>

                            <!-- Browse Button -->
                            <div class="browse-button">
                                <i class="fas fa-folder-open mr-2"></i>
                                Browse Files
                            </div>
                        </div>

                        <!-- PDF Preview -->
                        <div id="pdfPreview" class="pdf-preview hidden">
                            <div class="pdf-preview-content">
                                <div class="pdf-icon">
                                    <i class="fas fa-file-pdf text-red-500 text-3xl"></i>
                                </div>
                                <div class="pdf-info">
                                    <div class="pdf-name" id="pdfFileName"></div>
                                    <div class="pdf-size" id="pdfFileSize"></div>
                                </div>
                                <button type="button" class="remove-pdf" id="removePdf">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeAddOrdinanceModal()"
                            class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" id="ordinanceSubmitBtn"
                            class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                        <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        Create Ordinance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* PDF Upload Styles */
.pdf-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
    position: relative;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.pdf-upload-area:hover {
    border-color: #ef4444;
    background: #fef2f2;
}

.pdf-upload-area.dragover {
    border-color: #ef4444;
    background: #fef2f2;
    transform: scale(1.02);
}

.pdf-upload-area.uploading {
    border-color: #3b82f6;
    background: #eff6ff;
}

.pdf-upload-area.success {
    border-color: #10b981;
    background: #f0fdf4;
}

.upload-hint {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    background: #ef4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    opacity: 0.8;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.upload-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.upload-subtext {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.browse-button {
    background: #ef4444;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.2s;
}

.browse-button:hover {
    background: #dc2626;
}

/* PDF Preview Styles */
.pdf-preview {
    border: 2px solid #10b981;
    border-radius: 12px;
    padding: 1rem;
    background: #f0fdf4;
    margin-top: 1rem;
}

.pdf-preview-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pdf-icon {
    flex-shrink: 0;
}

.pdf-info {
    flex-grow: 1;
}

.pdf-name {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.pdf-size {
    font-size: 0.875rem;
    color: #6b7280;
}

.remove-pdf {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

.remove-pdf:hover {
    background: #dc2626;
}
</style>
