{% extends 'base.html' %}

{% block title %}Delete Banner - Admin{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-trash text-red-600 mr-3"></i>
                    Delete Banner
                </h1>
                <p class="text-gray-600 mt-2">
                    Permanently remove this banner from the system
                </p>
            </div>
            <a href="{% url 'ordinances:admin_banner_list' %}"
               class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to Banners
            </a>
        </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <!-- Warning Header -->
        <div class="bg-red-50 border-b border-red-200 px-6 py-4">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl mr-3"></i>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">Confirm Deletion</h3>
                    <p class="text-red-700 text-sm">This action cannot be undone.</p>
                </div>
            </div>
        </div>

        <!-- Banner Details -->
        <div class="px-6 py-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Banner to be deleted:</h4>
            
            <!-- Banner Preview -->
            <div class="{{ banner.get_css_classes }} py-3 px-4 rounded-lg border-2 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2 flex-1">
                        {% if banner.show_icon %}
                            <i class="{{ banner.get_icon_class }}"></i>
                        {% endif %}
                        <div>
                            <span class="font-semibold text-sm">{{ banner.title }}:</span>
                            <span class="ml-2 text-sm">{{ banner.message }}</span>
                        </div>
                    </div>
                    {% if banner.is_dismissible %}
                    <button type="button" class="hover:opacity-75 transition-opacity ml-4 p-1" disabled>
                        <i class="fas fa-times text-sm"></i>
                    </button>
                    {% endif %}
                </div>
            </div>

            <!-- Banner Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <h5 class="font-medium text-gray-900 mb-2">Banner Details</h5>
                    <dl class="space-y-2 text-sm">
                        <div>
                            <dt class="text-gray-500">Type:</dt>
                            <dd class="text-gray-900">{{ banner.get_banner_type_display }}</dd>
                        </div>
                        <div>
                            <dt class="text-gray-500">Color Scheme:</dt>
                            <dd class="text-gray-900">{{ banner.get_color_scheme_display }}</dd>
                        </div>
                        <div>
                            <dt class="text-gray-500">Priority:</dt>
                            <dd class="text-gray-900">{{ banner.priority }}</dd>
                        </div>
                        <div>
                            <dt class="text-gray-500">Status:</dt>
                            <dd class="text-gray-900">
                                {% if banner.is_currently_active %}
                                    <span class="text-green-600">Active</span>
                                {% else %}
                                    <span class="text-red-600">Inactive</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>
                
                <div>
                    <h5 class="font-medium text-gray-900 mb-2">Schedule</h5>
                    <dl class="space-y-2 text-sm">
                        <div>
                            <dt class="text-gray-500">Start Date:</dt>
                            <dd class="text-gray-900">{{ banner.start_date|date:"M d, Y H:i" }}</dd>
                        </div>
                        <div>
                            <dt class="text-gray-500">End Date:</dt>
                            <dd class="text-gray-900">
                                {% if banner.end_date %}
                                    {{ banner.end_date|date:"M d, Y H:i" }}
                                {% else %}
                                    <span class="text-gray-400">No end date</span>
                                {% endif %}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-gray-500">Created:</dt>
                            <dd class="text-gray-900">{{ banner.created_at|date:"M d, Y H:i" }}</dd>
                        </div>
                        <div>
                            <dt class="text-gray-500">Created By:</dt>
                            <dd class="text-gray-900">
                                {% if banner.created_by %}
                                    {{ banner.created_by.get_full_name|default:banner.created_by.username }}
                                {% else %}
                                    <span class="text-gray-400">Unknown</span>
                                {% endif %}
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Settings Summary -->
            <div class="mb-6">
                <h5 class="font-medium text-gray-900 mb-2">Settings</h5>
                <div class="flex flex-wrap gap-2">
                    {% if banner.is_dismissible %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <i class="fas fa-times mr-1"></i>Dismissible
                        </span>
                    {% endif %}
                    {% if banner.show_icon %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-eye mr-1"></i>Show Icon
                        </span>
                    {% endif %}
                    {% if not banner.is_active %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <i class="fas fa-pause mr-1"></i>Inactive
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-4">
            <a href="{% url 'ordinances:admin_banner_list' %}" 
               class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors">
                <i class="fas fa-times mr-2"></i>Cancel
            </a>
            <a href="{% url 'ordinances:admin_banner_edit' banner.id %}" 
               class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                <i class="fas fa-edit mr-2"></i>Edit Instead
            </a>
            <form method="post" class="inline">
                {% csrf_token %}
                <button type="submit" 
                        onclick="return confirm('Are you absolutely sure you want to delete this banner? This action cannot be undone.')"
                        class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fas fa-trash mr-2"></i>Delete Banner
                </button>
            </form>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-600 mr-3 mt-1"></i>
            <div class="text-blue-800">
                <h4 class="font-semibold mb-1">Alternative Actions</h4>
                <p class="text-sm">
                    Instead of deleting, you can:
                </p>
                <ul class="text-sm mt-2 space-y-1">
                    <li>• Set the banner to "Inactive" to hide it from the website</li>
                    <li>• Set an end date to automatically hide it after a specific time</li>
                    <li>• Edit the banner content if there are errors</li>
                </ul>
                <div class="mt-3 space-x-2">
                    <a href="{% url 'ordinances:admin_banner_edit' banner.id %}" 
                       class="text-blue-600 hover:underline text-sm">
                        Edit Banner Instead
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
