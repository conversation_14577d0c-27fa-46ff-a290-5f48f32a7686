from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile
from ordinances.models import Ordinance, Category
from django.contrib.auth import get_user_model
import io

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a test ordinance with a simple PDF for testing the secure PDF viewer'

    def handle(self, *args, **options):
        # Create a simple PDF content
        pdf_content = b'''%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 100
>>
stream
BT
/F1 24 Tf
100 700 Td
(TEST ORDINANCE PDF) Tj
100 650 Td
(This is a test PDF for the secure viewer.) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000424 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
521
%%EOF'''

        # Get or create admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            self.stdout.write(self.style.ERROR('No superuser found. Please create one first.'))
            return

        # Get or create test category
        category, created = Category.objects.get_or_create(
            name='Test Category',
            defaults={'slug': 'test-category'}
        )

        # Check if test ordinance already exists
        ordinance_number = 'TEST-PDF-001'
        if Ordinance.objects.filter(ordinance_number=ordinance_number).exists():
            self.stdout.write(f'Test ordinance {ordinance_number} already exists.')
            return

        # Create test ordinance
        ordinance = Ordinance.objects.create(
            ordinance_number=ordinance_number,
            title='Test Ordinance for PDF Viewer',
            content='''This is a test ordinance created specifically for testing the secure PDF viewer functionality.

WHEREAS, testing is important for ensuring system functionality;
WHEREAS, the secure PDF viewer needs to be properly tested;
WHEREAS, this test ordinance serves that purpose;

NOW, THEREFORE, BE IT ORDAINED that:

Section 1. This is a test ordinance for PDF viewer testing.
Section 2. The PDF attached to this ordinance should display in the secure viewer.
Section 3. Watermarks and security features should be visible.
Section 4. This ordinance is for testing purposes only.''',
            year_passed=2024,
            status='published',
            category=category,
            created_by=admin_user,
            updated_by=admin_user
        )

        # Attach the PDF file
        pdf_file = ContentFile(pdf_content, name=f'{ordinance_number}.pdf')
        ordinance.pdf_file.save(f'{ordinance_number}.pdf', pdf_file, save=True)

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created test ordinance: {ordinance_number}')
        )
        self.stdout.write(f'Ordinance URL: /ordinances/{ordinance.slug}/')
        self.stdout.write(f'PDF Viewer URL: /ordinances/{ordinance.slug}/view-pdf/')
        self.stdout.write(f'PDF file saved to: {ordinance.pdf_file.name}')
