{% extends 'auth_base.html' %}
{% load static %}

{% block title %}Ad<PERSON>gin - Municipality of Dumingag{% endblock %}

{% block extra_css %}
<style>
/* Enhanced Login Page Styling */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #123458 0%, #1e3a5f 30%, #2a4a6b 70%, #3a5a7b 100%);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(241, 239, 236, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(212, 201, 190, 0.1) 0%, transparent 50%),
        url("{% static 'img/dumingag-logo.png' %}") center/400px no-repeat;
    opacity: 0.05;
    pointer-events: none;
}

.login-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(241, 239, 236, 0.02) 50%, transparent 70%);
    pointer-events: none;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(25px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 32px;
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.3),
        0 16px 32px rgba(18, 52, 88, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    max-width: 480px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #123458 0%, #2563eb 50%, #123458 100%);
    border-radius: 32px 32px 0 0;
}

.security-header {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
    color: white;
    padding: 20px;
    margin: 20px 20px 0 20px;
    border-radius: 20px;
    text-align: center;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
    position: relative;
    overflow: hidden;
}

.security-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.form-group {
    margin-bottom: 28px;
    position: relative;
}

.form-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
    font-size: 15px;
    letter-spacing: 0.025em;
}

.form-input {
    width: 100%;
    padding: 18px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.06),
        0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.form-input:focus {
    outline: none;
    border-color: #2563eb;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
        0 0 0 4px rgba(37, 99, 235, 0.1),
        0 8px 25px rgba(37, 99, 235, 0.15),
        inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.form-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.form-group.focused .form-label {
    color: #2563eb;
    transform: translateY(-2px);
}

.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #123458 0%, #2563eb 50%, #1d4ed8 100%);
    color: white;
    padding: 20px 24px;
    border: none;
    border-radius: 16px;
    font-weight: 700;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 8px 25px rgba(37, 99, 235, 0.3),
        0 4px 12px rgba(18, 52, 88, 0.2);
    letter-spacing: 0.025em;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.login-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 16px 40px rgba(37, 99, 235, 0.4),
        0 8px 20px rgba(18, 52, 88, 0.3);
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #2563eb 100%);
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.error-message {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border: 2px solid #fca5a5;
    color: #dc2626;
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 24px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
    display: flex;
    align-items: center;
}

.error-message i {
    margin-right: 12px;
    font-size: 16px;
}

.back-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    padding: 12px 20px;
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    font-size: 14px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
}

.back-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.security-features {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #bae6fd;
    border-radius: 16px;
    padding: 20px;
    margin-top: 24px;
    box-shadow:
        inset 0 1px 2px rgba(59, 130, 246, 0.1),
        0 2px 8px rgba(59, 130, 246, 0.05);
}

.loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.form-input.loading {
    pointer-events: none;
    opacity: 0.7;
}

.login-card {
    animation: fadeInUp 0.8s ease-out;
}

.form-group {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }

.security-features {
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* Responsive Design */
@media (max-width: 640px) {
    .login-card {
        margin: 20px;
        border-radius: 24px;
        max-width: none;
    }

    .login-card .p-10 {
        padding: 24px;
    }

    .security-header {
        margin: 16px 16px 0 16px;
        padding: 16px;
    }

    .form-input {
        padding: 16px 18px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .login-btn {
        padding: 18px 20px;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }

    .back-link {
        position: static;
        margin-top: 20px;
        align-self: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="login-container flex items-center justify-center p-4">
    <div class="login-card relative z-10">
        <!-- Security Header -->
        <div class="security-header">
            <i class="fas fa-shield-alt mr-2"></i>
            SECURE ADMIN ACCESS
        </div>

        <!-- Login Form -->
        <div class="p-10">
            <!-- Logo and Title -->
            <div class="text-center mb-10">
                <div class="relative inline-block mb-6">
                    <div class="w-24 h-24 mx-auto mb-4 relative">
                        <img src="{% static 'img/dumingag-logo.png' %}"
                             alt="Dumingag Logo"
                             class="w-full h-full object-contain drop-shadow-lg">
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-xl"></div>
                    </div>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-3 tracking-tight">Admin Login</h1>
                <p class="text-gray-600 text-lg font-medium">Municipality of Dumingag</p>
                <div class="w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mt-4 rounded-full"></div>
            </div>

            <!-- Error Messages -->
            {% if form.errors %}
                <div class="error-message">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {% for field, errors in form.errors.items %}
                        {% for error in errors %}
                            {{ error }}
                        {% endfor %}
                    {% endfor %}
                </div>
            {% endif %}

            <!-- Login Form -->
            <form method="post" id="loginForm">
                {% csrf_token %}

                <div class="form-group">
                    <label for="{{ form.username.id_for_label }}" class="form-label">
                        <i class="fas fa-user mr-2 text-blue-500"></i>
                        Username
                    </label>
                    <input type="text"
                           name="{{ form.username.name }}"
                           id="{{ form.username.id_for_label }}"
                           class="form-input"
                           placeholder="Enter your username"
                           required
                           autocomplete="username">
                </div>

                <div class="form-group">
                    <label for="{{ form.password.id_for_label }}" class="form-label">
                        <i class="fas fa-lock mr-2 text-blue-500"></i>
                        Password
                    </label>
                    <input type="password"
                           name="{{ form.password.name }}"
                           id="{{ form.password.id_for_label }}"
                           class="form-input"
                           placeholder="Enter your password"
                           required
                           autocomplete="current-password">
                </div>

                <button type="submit" class="login-btn" id="loginButton">
                    <span id="loginText">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Login to Admin Panel
                    </span>
                    <div class="loading-spinner" id="loadingSpinner"></div>
                </button>
            </form>

            <!-- Security Features -->
            <div class="security-features">
                <h4 class="text-sm font-bold text-blue-800 mb-4 flex items-center">
                    <i class="fas fa-shield-check mr-2 text-blue-600"></i>
                    Security Features
                </h4>
                <div class="grid grid-cols-1 gap-3">
                    <div class="flex items-center text-sm text-blue-700">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-lock text-blue-600 text-xs"></i>
                        </div>
                        <div>
                            <div class="font-semibold">Secure SSL Connection</div>
                            <div class="text-xs text-blue-600">256-bit encryption</div>
                        </div>
                    </div>
                    <div class="flex items-center text-sm text-blue-700">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-eye text-blue-600 text-xs"></i>
                        </div>
                        <div>
                            <div class="font-semibold">Login Attempts Monitored</div>
                            <div class="text-xs text-blue-600">Real-time security</div>
                        </div>
                    </div>
                    <div class="flex items-center text-sm text-blue-700">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-clock text-blue-600 text-xs"></i>
                        </div>
                        <div>
                            <div class="font-semibold">Session Timeout: 30 minutes</div>
                            <div class="text-xs text-blue-600">Auto-logout protection</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Public Site -->
    <div class="absolute top-4 left-4">
        <a href="{% url 'ordinances:home' %}" class="back-link">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Public Site
        </a>
    </div>
</div>

<!-- Login Form Enhancement Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const button = document.getElementById('loginButton');
    const loginText = document.getElementById('loginText');
    const spinner = document.getElementById('loadingSpinner');
    const inputs = form.querySelectorAll('.form-input');

    // Form submission handling
    form.addEventListener('submit', function(e) {
        // Show loading state
        button.disabled = true;
        loginText.style.display = 'none';
        spinner.style.display = 'block';

        // Add loading class to inputs
        inputs.forEach(input => input.classList.add('loading'));

        // Log login attempt
        console.log('Admin login attempt at:', new Date().toISOString());
    });

    // Input focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // Auto-focus username field
    const usernameField = document.querySelector('input[name="username"]');
    if (usernameField) {
        usernameField.focus();
    }
});
</script>
{% endblock %}

{% block extra_js %}
<script>
// Additional enhancements for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling for any anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && e.target.tagName !== 'BUTTON' && e.target.type !== 'submit') {
            const form = document.getElementById('loginForm');
            if (form) {
                form.submit();
            }
        }
    });

    // Add visual feedback for form validation
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('invalid', function(e) {
            e.preventDefault();
            this.classList.add('border-red-500');
            this.classList.remove('border-gray-300');
        });

        input.addEventListener('input', function() {
            if (this.validity.valid) {
                this.classList.remove('border-red-500');
                this.classList.add('border-gray-300');
            }
        });
    });
});
</script>
{% endblock %}