<!-- Django Messages Display -->
{% if messages %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} {% if message.tags == 'error' %}message-error{% elif message.tags == 'success' %}message-success{% else %}message-success{% endif %} px-4 py-3 rounded mb-4"
                 x-data="{ show: true }"
                 x-show="show"
                 x-transition>
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        {% if message.tags == 'success' %}
                            <i class="fas fa-check-circle mr-2 text-green-500"></i>
                        {% elif message.tags == 'error' %}
                            <i class="fas fa-exclamation-circle mr-2 text-red-500"></i>
                        {% elif message.tags == 'warning' %}
                            <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i>
                        {% else %}
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                        {% endif %}
                        <span>{{ message }}</span>
                    </div>
                    <button @click="show = false" 
                            class="ml-4 text-lg font-bold hover:opacity-75 transition-opacity" 
                            data-dismiss="message">
                        &times;
                    </button>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}
