# Secure PDF Viewer Implementation

## Overview

This implementation provides a secure, read-only PDF viewer for ordinances with watermarks and access tracking, replacing direct PDF downloads with an embedded viewer that includes security measures.

## Features

### 🔒 Security Features
- **Read-only viewing**: PDFs are embedded in an iframe with restricted toolbar access
- **Watermarks**: Visual watermarks with "CONFIDENTIAL" and "OFFICIAL DOCUMENT" labels
- **Access tracking**: Records user IP, access time, and session information
- **Download prevention**: Disables right-click, text selection, and keyboard shortcuts
- **User identification**: Shows authenticated user info or "Guest" for anonymous users

### 📱 User Experience
- **Responsive design**: Works on desktop and mobile devices
- **Professional interface**: Clean, government-appropriate styling
- **Easy navigation**: Breadcrumb navigation and back-to-details button
- **Print support**: Allows printing while maintaining security notices

## Implementation Details

### Files Modified/Created

1. **`ordinances/views.py`**
   - Added `secure_pdf_viewer()` function
   - Creates security context with user tracking

2. **`ordinances/urls.py`**
   - Added URL pattern: `ordinances/<slug:slug>/view-pdf/`

3. **`templates/ordinances/secure_pdf_viewer.html`**
   - New template for secure PDF viewing
   - Includes watermarks and security notices
   - JavaScript for disabling certain user interactions

4. **`templates/ordinances/ordinance_detail.html`**
   - Updated "Official PDF" button to use secure viewer
   - Changed from download link to view link

5. **`templates/ordinances/admin/partials/ordinance_list.html`**
   - Updated admin PDF links to use secure viewer

### Security Context

The viewer tracks the following information:
```python
security_context = {
    'user_ip': request.META.get('REMOTE_ADDR', 'Unknown'),
    'access_time': timezone.now().strftime('%Y-%m-%d %H:%M:%S'),
    'session_id': request.session.session_key or 'No Session',
}
```

### Watermark Implementation

- **Visual overlay**: CSS-based diagonal stripes with low opacity
- **Corner badges**: "CONFIDENTIAL" and "OFFICIAL DOCUMENT" labels
- **User information**: Displays in security notice box
- **Access details**: Shows IP, timestamp, and session info

## Usage

### For Public Users
1. Navigate to an ordinance detail page
2. Click "View Official PDF" button (if PDF exists)
3. View PDF in secure viewer with watermarks
4. Print if needed (watermarks included)

### For Administrators
- Same functionality as public users
- Admin panel links also use secure viewer
- Can still access generated PDF downloads

## Browser Compatibility

- **Chrome/Edge**: Full functionality
- **Firefox**: Full functionality
- **Safari**: Full functionality
- **Mobile browsers**: Responsive design works on all major mobile browsers

## Security Measures

### JavaScript Protections
```javascript
// Disable right-click context menu
document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
    return false;
});

// Disable text selection
document.addEventListener('selectstart', function(e) {
    e.preventDefault();
    return false;
});

// Disable certain keyboard shortcuts (F12, Ctrl+U, etc.)
```

### PDF Iframe Configuration
```html
<iframe 
    src="{{ pdf_url }}#toolbar=0&navpanes=0&scrollbar=1&view=FitH"
    oncontextmenu="return false;">
```

## Testing

Comprehensive test suite included in `ordinances/tests/test_secure_pdf_viewer.py`:

- URL accessibility tests
- Context validation
- Template rendering
- Security feature presence
- User authentication scenarios
- Redirect behavior for ordinances without PDFs

Run tests with:
```bash
python manage.py test ordinances.tests.test_secure_pdf_viewer
```

## Future Enhancements

### Potential Improvements
1. **Enhanced watermarking**: Server-side PDF watermark injection
2. **Access logging**: Database logging of PDF access attempts
3. **Time-limited access**: Session-based PDF access tokens
4. **Advanced DRM**: More sophisticated document protection
5. **Audit trail**: Detailed access reporting for administrators

### Configuration Options
Consider adding settings for:
- Watermark text customization
- Security level configuration
- Access logging preferences
- Print permission controls

## Notes

- This implementation balances security with usability
- Watermarks are visual deterrents, not absolute protection
- For maximum security, consider server-side PDF processing
- Regular security audits recommended for government documents

## Reference Implementation

Based on the digital hub paper viewer pattern from:
https://github.com/lanzy-lanzy/digitalhubv2/blob/main/scholar/templates/scholar/paper_detail.html
